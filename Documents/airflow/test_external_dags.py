#!/usr/bin/env python3
"""
Quick test script to check the status of external DAGs
Run this to see what's actually in the database
"""

import sys
import os

# Add the dags directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags'))

def check_external_dag_status():
    try:
        from airflow.models import DagRun, TaskInstance
        from airflow.utils.db import provide_session
        
        external_dags = [
            "run_intellisource_wc_patientaccounts_etl_dag",
            "analytics_groups_cron_dag", 
            "run_intellisource_etl_dag"
        ]
        
        @provide_session
        def check_all_dags(session=None):
            for ext_dag in external_dags:
                print(f"\n{'='*60}")
                print(f"CHECKING: {ext_dag}")
                print(f"{'='*60}")
                
                # Get recent DAG runs
                recent_runs = session.query(DagRun).filter(
                    DagRun.dag_id == ext_dag
                ).order_by(DagRun.execution_date.desc()).limit(5).all()
                
                if not recent_runs:
                    print(f"❌ No DAG runs found for {ext_dag}")
                    continue
                    
                print(f"📊 Found {len(recent_runs)} recent DAG runs:")
                
                for i, run in enumerate(recent_runs):
                    print(f"\n  🔹 Run #{i+1}: {run.run_id}")
                    print(f"     State: {run.state}")
                    print(f"     Date: {run.execution_date}")
                    print(f"     Start: {run.start_date}")
                    print(f"     End: {run.end_date}")
                    
                    # Get all tasks in this run
                    tasks = session.query(TaskInstance).filter(
                        TaskInstance.dag_id == ext_dag,
                        TaskInstance.execution_date == run.execution_date
                    ).all()
                    
                    if tasks:
                        print(f"     Tasks in this run:")
                        for task in tasks:
                            print(f"       - {task.task_id}: {task.state}")
                            if task.state == 'success':
                                print(f"         ✅ SUCCESS at {task.end_date}")
                            elif task.state == 'failed':
                                print(f"         ❌ FAILED at {task.end_date}")
                    else:
                        print(f"     ⚠️  No tasks found in this run")
                
                # Find the latest successful run
                successful_runs = [run for run in recent_runs if run.state == 'success']
                if successful_runs:
                    latest_success = successful_runs[0]
                    print(f"\n  ✅ LATEST SUCCESSFUL RUN:")
                    print(f"     Run: {latest_success.run_id}")
                    print(f"     Date: {latest_success.execution_date}")
                else:
                    print(f"\n  ❌ NO SUCCESSFUL RUNS FOUND")
        
        check_all_dags()
        
    except Exception as e:
        print(f"❌ Error checking DAG status: {e}")
        print("Make sure you're running this from the Airflow environment")

if __name__ == "__main__":
    print("🔍 EXTERNAL DAG STATUS CHECKER")
    print("=" * 60)
    check_external_dag_status()
    print("\n" + "=" * 60)
    print("✅ Check complete!")
