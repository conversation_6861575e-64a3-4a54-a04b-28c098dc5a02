
import sys
import os
sys.path.append(os.path.dirname(__file__))
from airflow import DAG
from dag_utils_class import DagBuilder
from utilities.task_sensor import sensor
from utilities.send_sns_notification import get_on_failure_callback

schedule = "50 9 * * *"
script_path = "/home/<USER>/master_script.sh"

dag_builder = DagBuilder(schedule=schedule, dag_file_path=__file__)
dag = dag_builder.create_dag()

# Define sensor configurations

sensor_configs = [
   {"ext_dag": "run_intellisource_wc_patientaccounts_etl_dag"},
   {"ext_dag": "analytics_groups_cron_dag"},
   {"ext_dag": "run_intellisource_etl_dag", "minutes_delta": 2},
]

failure_callback = get_on_failure_callback(dag_builder.server_name, dag_builder.env)

sensor_tasks = [
   sensor(**config, dag=dag, on_failure_callback=failure_callback)
   for config in sensor_configs
]

# Create SSH task using DagBuilder
ssh_task = dag_builder.create_ssh_task(script_path,on_failure_callback=failure_callback)

with dag:
   sensor_tasks >> ssh_task


