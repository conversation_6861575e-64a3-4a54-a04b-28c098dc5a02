
import sys
import os
sys.path.append(os.path.dirname(__file__))
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.models import DagRun, TaskInstance
from airflow.utils.db import provide_session
from dag_utils_class import Dag<PERSON>uilder

schedule = "50 9 * * *"
script_path = "/home/<USER>/master_script.sh"

dag_builder = DagBuilder(schedule=schedule, dag_file_path=__file__)
dag = dag_builder.create_dag()

# Function to check external DAG status
@provide_session
def check_external_dag_status(external_dag_id, external_task_id, session=None):
    """Check if the latest run of external DAG/task is successful"""
    print(f"Checking status for {external_dag_id} -> {external_task_id}")

    # Get the most recent DAG run
    latest_dag_run = session.query(DagRun).filter(
        DagRun.dag_id == external_dag_id
    ).order_by(DagRun.execution_date.desc()).first()

    if not latest_dag_run:
        raise Exception(f"No DAG runs found for {external_dag_id}")

    print(f"Latest DAG run: {latest_dag_run.run_id}, State: {latest_dag_run.state}")

    # Get the task instance from the latest DAG run
    task_instance = session.query(TaskInstance).filter(
        TaskInstance.dag_id == external_dag_id,
        TaskInstance.task_id == external_task_id,
        TaskInstance.execution_date == latest_dag_run.execution_date
    ).first()

    if not task_instance:
        raise Exception(f"Task {external_task_id} not found in latest run of {external_dag_id}")

    print(f"Task {external_task_id} state: {task_instance.state}")

    if task_instance.state != 'success':
        raise Exception(f"Task {external_dag_id}.{external_task_id} is not successful. Current state: {task_instance.state}")

    print(f"✅ {external_dag_id}.{external_task_id} is successful!")
    return True

# Define external DAG checks
external_checks = [
    {"external_dag_id": "run_intellisource_wc_patientaccounts_etl_dag", "external_task_id": "run_wc_patientaccounts_etl"},
    {"external_dag_id": "analytics_groups_cron_dag", "external_task_id": "run_analytics_groups"},
    {"external_dag_id": "run_intellisource_etl_dag", "external_task_id": "run_intellisource_etl"},
]

# Create check tasks
check_tasks = []
for i, check in enumerate(external_checks):
    task = PythonOperator(
        task_id=f"check_{check['external_dag_id'].replace('_dag', '')}",
        python_callable=check_external_dag_status,
        op_kwargs={
            'external_dag_id': check['external_dag_id'],
            'external_task_id': check['external_task_id']
        },
        dag=dag
    )
    check_tasks.append(task)

# Create SSH task using DagBuilder
ssh_task = dag_builder.create_ssh_task(script_path)

with dag:
   check_tasks >> ssh_task


