from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago

def task_analytics_groups():
    print("Running analytics groups cron...")

with DAG(
    dag_id="analytics_groups_cron_dag",
    default_args={"owner": "airflow", "start_date": days_ago(1)},
    schedule_interval=None,
    catchup=False,
) as dag:

    run_task = PythonOperator(
        task_id="run_analytics_groups",
        python_callable=task_analytics_groups,
    )
