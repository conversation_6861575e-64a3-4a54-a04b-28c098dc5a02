from airflow.sensors.base import BaseSensorOperator
from airflow.utils.decorators import apply_defaults


class MWAACompatibleExternalTaskSensor(BaseSensorOperator):
    """
    MWAA-compatible sensor that directly checks external DAG/task status
    """

    @apply_defaults
    def __init__(self, external_dag_id, external_task_id=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.external_dag_id = external_dag_id
        self.external_task_id = external_task_id

    def poke(self, context):
        """Check the latest run of external DAG/task"""
        from airflow.models import DagRun, TaskInstance
        from airflow.utils.db import provide_session

        @provide_session
        def check_external_status(session=None):
            # Get the most recent DAG run
            latest_run = session.query(DagRun).filter(
                DagRun.dag_id == self.external_dag_id
            ).order_by(DagRun.execution_date.desc()).first()

            if not latest_run:
                self.log.error(f"No DAG runs found for {self.external_dag_id}")
                return False

            self.log.info(f"Latest DAG run: {latest_run.run_id}, State: {latest_run.state}")

            # Auto-discover task ID if not provided
            task_id = self.external_task_id
            if not task_id:
                # Get task IDs from latest run
                tasks = session.query(TaskInstance.task_id).filter(
                    TaskInstance.dag_id == self.external_dag_id,
                    TaskInstance.execution_date == latest_run.execution_date
                ).distinct().all()

                if tasks:
                    task_ids = [t.task_id for t in tasks]
                    self.log.info(f"Found tasks in latest run: {task_ids}")

                    # For single task, use it
                    if len(task_ids) == 1:
                        task_id = task_ids[0]
                    else:
                        # Prefer _task suffix
                        for tid in task_ids:
                            if tid.endswith('_task'):
                                task_id = tid
                                break
                        if not task_id:
                            task_id = task_ids[0]

                    self.log.info(f"Selected task ID: {task_id}")
                else:
                    # Fallback to pattern
                    task_id = self.external_dag_id.replace('_dag', '_task')
                    self.log.info(f"Using fallback task ID: {task_id}")

            # Check task status in latest run
            task_instance = session.query(TaskInstance).filter(
                TaskInstance.dag_id == self.external_dag_id,
                TaskInstance.task_id == task_id,
                TaskInstance.execution_date == latest_run.execution_date
            ).first()

            if not task_instance:
                self.log.error(f"Task {task_id} not found in latest run")
                return False

            self.log.info(f"Task {task_id} state: {task_instance.state}")

            # Return True only if task is successful
            if task_instance.state == 'success':
                self.log.info(f"Task {task_id} is successful!")
                return True
            elif task_instance.state == 'failed':
                self.log.error(f"Task {task_id} failed in latest run")
                raise Exception(f"External task {self.external_dag_id}.{task_id} failed")
            else:
                self.log.info(f"Task {task_id} is in state '{task_instance.state}', waiting...")
                return False

        return check_external_status()


def sensor(ext_dag, dag, minutes_delta=0, on_failure_callback=None, external_task_id=None):
    """
    MWAA-compatible sensor that checks the latest run of an external DAG.
    Uses a custom sensor implementation that works reliably in MWAA.
    """

    # Create MWAA-compatible sensor
    return MWAACompatibleExternalTaskSensor(
        task_id=f"wait_for_{ext_dag.replace(' ', '_')}",
        external_dag_id=ext_dag,
        external_task_id=external_task_id,
        mode='reschedule',
        poke_interval=60,
        timeout=3600,  # 1 hour timeout
        retries=0,
        dag=dag,
        on_failure_callback=on_failure_callback
    )