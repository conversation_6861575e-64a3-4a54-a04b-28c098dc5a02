from airflow.sensors.external_task import ExternalTaskSensor

def sensor(ext_dag, dag, minutes_delta=0,on_failure_callback=None,external_task_id=None):
    """  The function sets up an ExternalTaskSensor to wait for a task in an external DAG to complete,
         with customizable task_id, external_dag_id, and execution_delta. If task_id is not provided, it defaults to wait_for_{dag}.
         The sensor keeps an eye on the external task every minute and will do so for 24 hrs before timing out.
    """

    def _infer_external_task_id(dag_id):
        """
        Dynamically infer the external task ID using multiple strategies:
        1. Database query for existing task instances
        2. DAG introspection if DAG is loaded
        3. Pattern-based fallback
        """

        # Strategy 1: Query database for existing task instances
        def get_task_ids_from_database():
            try:
                from airflow.models import TaskInstance
                from airflow.utils.db import provide_session

                @provide_session
                def query_task_ids(session=None):
                    task_instances = session.query(TaskInstance.task_id).filter(
                        TaskInstance.dag_id == dag_id
                    ).distinct().all()

                    if task_instances:
                        task_ids = [ti.task_id for ti in task_instances]

                        # Prefer task IDs that start with 'run_' or contain 'task'
                        for task_id in task_ids:
                            if task_id.startswith('run_') or 'task' in task_id:
                                return task_id

                        # Return the first one if no preferred pattern
                        return task_ids[0]
                    return None

                return query_task_ids()

            except Exception as e:
                print(f"[INFO] Database query failed for {dag_id}: {e}")
                return None

        # Strategy 2: DAG introspection (if DAG is loaded in DagBag)
        def get_task_ids_from_dag_introspection():
            try:
                from airflow.models import DagBag

                dag_bag = DagBag()
                if dag_id in dag_bag.dags:
                    external_dag = dag_bag.dags[dag_id]
                    task_ids = list(external_dag.task_ids)

                    if task_ids:
                        # Prefer task IDs that start with 'run_' or contain 'task'
                        for task_id in task_ids:
                            if task_id.startswith('run_') or 'task' in task_id:
                                return task_id

                        # Return the first one if no preferred pattern
                        return task_ids[0]

            except Exception as e:
                print(f"[INFO] DAG introspection failed for {dag_id}: {e}")

            return None

        # Strategy 3: Pattern-based fallback
        def get_task_id_from_pattern():
            if dag_id.endswith('_dag'):
                return dag_id.replace('_dag', '_task')
            return dag_id

        # Try strategies in order
        task_id = get_task_ids_from_database()
        if task_id:
            print(f"[INFO] Found task ID '{task_id}' for DAG '{dag_id}' via database query")
            return task_id

        task_id = get_task_ids_from_dag_introspection()
        if task_id:
            print(f"[INFO] Found task ID '{task_id}' for DAG '{dag_id}' via DAG introspection")
            return task_id

        task_id = get_task_id_from_pattern()
        print(f"[INFO] Using pattern-based task ID '{task_id}' for DAG '{dag_id}'")
        return task_id

    task_id = f"wait_for_{ext_dag.replace(' ', '_')}"

    # Use provided external_task_id or infer it intelligently
    inferred_task_id = external_task_id or _infer_external_task_id(ext_dag)

    # Custom execution date function to always check the latest successful DAG run
    def execution_date_fn(dt):
        """
        Always check the most recent successful DAG run regardless of execution date.
        This ensures we pick up the latest status of external DAGs.
        """
        from airflow.models import DagRun
        from airflow.utils.db import provide_session

        @provide_session
        def get_latest_successful_execution_date(session=None):
            try:
                # Get the most recent successful DAG run
                latest_successful_run = session.query(DagRun).filter(
                    DagRun.dag_id == ext_dag,
                    DagRun.state == 'success'
                ).order_by(DagRun.execution_date.desc()).first()

                if latest_successful_run:
                    print(f"[INFO] Found latest successful run for {ext_dag}: {latest_successful_run.execution_date}")
                    return latest_successful_run.execution_date
                else:
                    print(f"[WARNING] No successful runs found for {ext_dag}")
                    return None

            except Exception as e:
                print(f"[ERROR] Could not query latest execution date for {ext_dag}: {e}")
                return None

        # Get the latest successful execution date from database
        latest_execution_date = get_latest_successful_execution_date()

        if latest_execution_date:
            # Return only the latest successful execution date
            return [latest_execution_date]
        else:
            # If no successful runs found, return empty list to make sensor fail
            print(f"[ERROR] No successful runs found for {ext_dag}, sensor will fail")
            return []

    ext_task_sensor = ExternalTaskSensor(
                    task_id=task_id,
                    external_dag_id=ext_dag,
                    external_task_id=inferred_task_id,
                    mode='reschedule',
                    poke_interval=60,
                    execution_date_fn=execution_date_fn,
                    timeout=86400,
                    failed_states=["failed", "upstream_failed"], # Fail fast on failure
                    retries=0, #No retries on sensor tasks, fails immediately.
                    dag=dag,
                    on_failure_callback=on_failure_callback,
                    allowed_states=['success'],
                    check_existence=True
                    )

    return ext_task_sensor