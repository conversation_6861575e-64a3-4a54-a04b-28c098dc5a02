from airflow.sensors.external_task import ExternalTaskSensor

def sensor(ext_dag, dag, minutes_delta=0, on_failure_callback=None, external_task_id=None):
    """
    Simple sensor that checks the latest successful run of an external DAG.
    Auto-discovers task IDs and always looks for the most recent successful execution.
    """

    def get_task_id():
        """Get external task ID - try multiple strategies with detailed logging"""
        if external_task_id:
            print(f"[INFO] Using provided external_task_id: {external_task_id}")
            return external_task_id

        print(f"[INFO] Auto-discovering task ID for DAG: {ext_dag}")

        # Strategy 1: Try database query
        try:
            from airflow.models import TaskInstance
            from airflow.utils.db import provide_session

            @provide_session
            def query_db(session=None):
                tasks = session.query(TaskInstance.task_id).filter(
                    TaskInstance.dag_id == ext_dag
                ).distinct().all()

                if tasks:
                    task_ids = [t.task_id for t in tasks]
                    print(f"[INFO] Found task IDs in database for {ext_dag}: {task_ids}")

                    # Prefer 'run_' tasks
                    for tid in task_ids:
                        if tid.startswith('run_'):
                            print(f"[INFO] Selected task ID (run_ preference): {tid}")
                            return tid

                    # If no 'run_' tasks, return the first one
                    selected_task = task_ids[0]
                    print(f"[INFO] Selected task ID (first available): {selected_task}")
                    return selected_task
                else:
                    print(f"[WARNING] No task instances found in database for {ext_dag}")
                    return None

            found_task = query_db()
            if found_task:
                return found_task
        except Exception as e:
            print(f"[WARNING] Database query failed for {ext_dag}: {e}")

        # Strategy 2: Try DAG introspection
        try:
            from airflow.models import DagBag

            print(f"[INFO] Trying DAG introspection for {ext_dag}")
            dag_bag = DagBag()
            if ext_dag in dag_bag.dags:
                external_dag_obj = dag_bag.dags[ext_dag]
                task_ids = list(external_dag_obj.task_ids)
                print(f"[INFO] Found task IDs via DAG introspection for {ext_dag}: {task_ids}")

                if task_ids:
                    # Prefer 'run_' tasks
                    for tid in task_ids:
                        if tid.startswith('run_'):
                            print(f"[INFO] Selected task ID (run_ preference): {tid}")
                            return tid

                    # If no 'run_' tasks, return the first one
                    selected_task = task_ids[0]
                    print(f"[INFO] Selected task ID (first available): {selected_task}")
                    return selected_task
            else:
                print(f"[WARNING] DAG {ext_dag} not found in DagBag")
        except Exception as e:
            print(f"[WARNING] DAG introspection failed for {ext_dag}: {e}")

        # Strategy 3: Fallback to pattern (matches task_runner.py logic)
        fallback_task = ext_dag.replace('_dag', '_task') if ext_dag.endswith('_dag') else ext_dag
        print(f"[INFO] Using fallback pattern task ID: {fallback_task}")
        print(f"[INFO] This matches the task_runner.py pattern: dag_id.replace('_dag', '_task')")
        return fallback_task

    def get_latest_run_date(dt):
        """Always return the latest DAG run execution date (regardless of DAG state)"""
        try:
            from airflow.models import DagRun
            from airflow.utils.db import provide_session

            @provide_session
            def query_latest(session=None):
                # Get the most recent DAG run (any state)
                latest_run = session.query(DagRun).filter(
                    DagRun.dag_id == ext_dag
                ).order_by(DagRun.execution_date.desc()).first()

                if not latest_run:
                    print(f"[ERROR] No DAG runs found for {ext_dag}")
                    return []

                print(f"[INFO] Latest DAG run for {ext_dag}: {latest_run.run_id}, State: {latest_run.state}, Date: {latest_run.execution_date}")

                # Always return the latest execution date - let ExternalTaskSensor handle the task state check
                return [latest_run.execution_date]

            return query_latest()
        except Exception as e:
            print(f"[ERROR] Failed to query latest run for {ext_dag}: {e}")
            return []

    # Create sensor
    return ExternalTaskSensor(
        task_id=f"wait_for_{ext_dag.replace(' ', '_')}",
        external_dag_id=ext_dag,
        external_task_id=get_task_id(),
        execution_date_fn=get_latest_run_date,
        mode='reschedule',
        poke_interval=60,
        timeout=86400,
        allowed_states=['success'],
        failed_states=['failed', 'upstream_failed'],
        retries=0,
        dag=dag,
        on_failure_callback=on_failure_callback
    )