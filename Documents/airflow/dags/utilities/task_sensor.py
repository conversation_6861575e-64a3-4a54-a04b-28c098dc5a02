from airflow.sensors.external_task import ExternalTaskSensor

def debug_external_dag_status(ext_dag):
    """Debug function to check what's actually in the database for an external DAG"""
    try:
        from airflow.models import DagRun, TaskInstance
        from airflow.utils.db import provide_session

        @provide_session
        def check_status(session=None):
            print(f"\n=== DEBUGGING {ext_dag} ===")

            # Get recent DAG runs
            recent_runs = session.query(DagRun).filter(
                DagRun.dag_id == ext_dag
            ).order_by(DagRun.execution_date.desc()).limit(5).all()

            if not recent_runs:
                print(f"❌ No DAG runs found for {ext_dag}")
                return

            print(f"📊 Recent DAG runs for {ext_dag}:")
            for run in recent_runs:
                print(f"  🔹 {run.run_id} | State: {run.state} | Date: {run.execution_date}")

                # Get all tasks in this run
                tasks = session.query(TaskInstance).filter(
                    TaskInstance.dag_id == ext_dag,
                    TaskInstance.execution_date == run.execution_date
                ).all()

                if tasks:
                    print(f"     Tasks in this run:")
                    for task in tasks:
                        print(f"       - {task.task_id}: {task.state}")
                else:
                    print(f"     No tasks found in this run")
            print(f"=== END DEBUG {ext_dag} ===\n")

        check_status()
    except Exception as e:
        print(f"❌ Debug failed for {ext_dag}: {e}")

def sensor(ext_dag, dag, minutes_delta=0, on_failure_callback=None, external_task_id=None):
    """
    Simple sensor that checks the latest successful run of an external DAG.
    Auto-discovers task IDs and always looks for the most recent successful execution.
    """

    # Debug: Show what's actually in the database for this external DAG (uncomment if needed)
    # debug_external_dag_status(ext_dag)

    def get_task_id():
        """Get external task ID - try multiple strategies with detailed logging"""
        if external_task_id:
            print(f"[INFO] Using provided external_task_id: {external_task_id}")
            return external_task_id

        print(f"[INFO] Auto-discovering task ID for DAG: {ext_dag}")

        # Strategy 1: Try database query
        try:
            from airflow.models import TaskInstance
            from airflow.utils.db import provide_session

            @provide_session
            def query_db(session=None):
                tasks = session.query(TaskInstance.task_id).filter(
                    TaskInstance.dag_id == ext_dag
                ).distinct().all()

                if tasks:
                    task_ids = [t.task_id for t in tasks]
                    print(f"[INFO] Found task IDs in database for {ext_dag}: {task_ids}")

                    # For single task DAGs, just return the only task
                    if len(task_ids) == 1:
                        selected_task = task_ids[0]
                        print(f"[INFO] Selected single task ID: {selected_task}")
                        return selected_task

                    # For multiple tasks, prefer 'run_' or '_task' patterns
                    for tid in task_ids:
                        if tid.startswith('run_') or tid.endswith('_task'):
                            print(f"[INFO] Selected task ID (pattern preference): {tid}")
                            return tid

                    # If no pattern match, return the first one
                    selected_task = task_ids[0]
                    print(f"[INFO] Selected task ID (first available): {selected_task}")
                    return selected_task
                else:
                    print(f"[WARNING] No task instances found in database for {ext_dag}")
                    return None

            found_task = query_db()
            if found_task:
                return found_task
        except Exception as e:
            print(f"[WARNING] Database query failed for {ext_dag}: {e}")

        # Strategy 2: Try DAG introspection
        try:
            from airflow.models import DagBag

            print(f"[INFO] Trying DAG introspection for {ext_dag}")
            dag_bag = DagBag()
            if ext_dag in dag_bag.dags:
                external_dag_obj = dag_bag.dags[ext_dag]
                task_ids = list(external_dag_obj.task_ids)
                print(f"[INFO] Found task IDs via DAG introspection for {ext_dag}: {task_ids}")

                if task_ids:
                    # For single task DAGs, just return the only task
                    if len(task_ids) == 1:
                        selected_task = task_ids[0]
                        print(f"[INFO] Selected single task ID: {selected_task}")
                        return selected_task

                    # For multiple tasks, prefer 'run_' or '_task' patterns
                    for tid in task_ids:
                        if tid.startswith('run_') or tid.endswith('_task'):
                            print(f"[INFO] Selected task ID (pattern preference): {tid}")
                            return tid

                    # If no pattern match, return the first one
                    selected_task = task_ids[0]
                    print(f"[INFO] Selected task ID (first available): {selected_task}")
                    return selected_task
            else:
                print(f"[WARNING] DAG {ext_dag} not found in DagBag")
        except Exception as e:
            print(f"[WARNING] DAG introspection failed for {ext_dag}: {e}")

        # Strategy 3: Fallback to pattern (matches task_runner.py logic)
        fallback_task = ext_dag.replace('_dag', '_task') if ext_dag.endswith('_dag') else ext_dag
        print(f"[INFO] Using fallback pattern task ID: {fallback_task}")
        print(f"[INFO] This matches the task_runner.py pattern: dag_id.replace('_dag', '_task')")
        return fallback_task

    def get_latest_run_date(dt):
        """Return the latest successful DAG run execution date"""
        try:
            from airflow.models import DagRun
            from airflow.utils.db import provide_session

            @provide_session
            def query_latest(session=None):
                # Get all recent DAG runs ordered by execution date (newest first)
                recent_runs = session.query(DagRun).filter(
                    DagRun.dag_id == ext_dag
                ).order_by(DagRun.execution_date.desc()).limit(10).all()

                if not recent_runs:
                    print(f"[ERROR] No DAG runs found for {ext_dag}")
                    return []

                print(f"[INFO] Found {len(recent_runs)} recent DAG runs for {ext_dag}")

                # Log all recent runs for debugging
                for run in recent_runs:
                    print(f"[INFO] DAG run: {run.run_id}, State: {run.state}, Date: {run.execution_date}")

                # Check the most recent DAG run first
                latest_run = recent_runs[0]
                print(f"[INFO] Most recent DAG run: {latest_run.run_id}, State: {latest_run.state}, Date: {latest_run.execution_date}")

                # If the most recent run is successful, use it
                if latest_run.state == 'success':
                    print(f"[SUCCESS] Most recent DAG run is successful!")
                    return [latest_run.execution_date]

                # If the most recent run failed, the sensor should fail
                elif latest_run.state == 'failed':
                    print(f"[FAILED] Most recent DAG run failed. Sensor should fail.")
                    return []  # Return empty to make sensor fail

                # If the most recent run is running, wait for it
                elif latest_run.state in ['running', 'queued']:
                    print(f"[WAITING] Most recent DAG run is still {latest_run.state}. Waiting...")
                    return []  # Return empty to keep waiting

                # For other states, look for the latest successful run
                else:
                    print(f"[INFO] Most recent run state is '{latest_run.state}'. Looking for latest successful run...")
                    for run in recent_runs:
                        if run.state == 'success':
                            print(f"[SUCCESS] Found latest successful DAG run: {run.run_id}, Date: {run.execution_date}")
                            return [run.execution_date]

                    # If no successful runs found at all
                    print(f"[ERROR] No successful DAG runs found for {ext_dag}")
                    return []

            return query_latest()
        except Exception as e:
            print(f"[ERROR] Failed to query latest run for {ext_dag}: {e}")
            return []

    # Create sensor
    return ExternalTaskSensor(
        task_id=f"wait_for_{ext_dag.replace(' ', '_')}",
        external_dag_id=ext_dag,
        external_task_id=get_task_id(),
        execution_date_fn=get_latest_run_date,
        mode='reschedule',
        poke_interval=60,
        timeout=86400,
        allowed_states=['success'],
        failed_states=['failed', 'upstream_failed'],
        retries=0,
        dag=dag,
        on_failure_callback=on_failure_callback
    )