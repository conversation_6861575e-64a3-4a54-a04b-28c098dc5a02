from datetime import timedelta
from airflow.sensors.external_task import ExternalTaskSensor

def sensor(ext_dag, dag, minutes_delta=0,on_failure_callback=None,external_task_id=None):
    """  The function sets up an ExternalTaskSensor to wait for a task in an external DAG to complete, 
         with customizable task_id, external_dag_id, and execution_delta. If task_id is not provided, it defaults to wait_for_{dag}. 
         The sensor keeps an eye on the external task every minute and will do so for 24 hrs before timing out.   
    """

    task_id = f"wait_for_{ext_dag.replace(' ', '_')}"
        
    ext_task_sensor = ExternalTaskSensor(
                    task_id=task_id,
                    external_dag_id=ext_dag,
                    external_task_id=external_task_id or ext_dag.replace('_dag', '_task'),
                    mode='reschedule',
                    poke_interval=60,
                    execution_delta=timedelta(minutes=minutes_delta),
                    timeout=86400,
                    failed_states=["failed", "upstream_failed"], # Fail fast on failure
                    retries=0, #No retries on sensor tasks, fails immediately. 
                    dag=dag,
                    on_failure_callback=on_failure_callback
                    )

    return ext_task_sensor