from airflow.sensors.external_task import ExternalTaskSensor

def sensor(ext_dag, dag, minutes_delta=0, on_failure_callback=None, external_task_id=None):
    """
    Simple sensor that checks the latest successful run of an external DAG.
    Auto-discovers task IDs and always looks for the most recent successful execution.
    """

    def get_task_id():
        """Get external task ID - try database first, then fallback to pattern"""
        if external_task_id:
            return external_task_id

        # Try to get from database
        try:
            from airflow.models import TaskInstance
            from airflow.utils.db import provide_session

            @provide_session
            def query_db(session=None):
                tasks = session.query(TaskInstance.task_id).filter(
                    TaskInstance.dag_id == ext_dag
                ).distinct().all()

                if tasks:
                    task_ids = [t.task_id for t in tasks]
                    # Prefer 'run_' tasks
                    for tid in task_ids:
                        if tid.startswith('run_'):
                            return tid
                    return task_ids[0]
                return None

            found_task = query_db()
            if found_task:
                return found_task
        except:
            pass

        # Fallback to pattern
        return ext_dag.replace('_dag', '_task') if ext_dag.endswith('_dag') else ext_dag

    def get_latest_run_date(dt):
        """Always return the latest DAG run execution date (regardless of DAG state)"""
        try:
            from airflow.models import DagRun
            from airflow.utils.db import provide_session

            @provide_session
            def query_latest(session=None):
                # Get the most recent DAG run (any state)
                latest_run = session.query(DagRun).filter(
                    DagRun.dag_id == ext_dag
                ).order_by(DagRun.execution_date.desc()).first()

                if not latest_run:
                    print(f"[ERROR] No DAG runs found for {ext_dag}")
                    return []

                print(f"[INFO] Latest DAG run for {ext_dag}: {latest_run.run_id}, State: {latest_run.state}, Date: {latest_run.execution_date}")

                # Always return the latest execution date - let ExternalTaskSensor handle the task state check
                return [latest_run.execution_date]

            return query_latest()
        except Exception as e:
            print(f"[ERROR] Failed to query latest run for {ext_dag}: {e}")
            return []

    # Create sensor
    return ExternalTaskSensor(
        task_id=f"wait_for_{ext_dag.replace(' ', '_')}",
        external_dag_id=ext_dag,
        external_task_id=get_task_id(),
        execution_date_fn=get_latest_run_date,
        mode='reschedule',
        poke_interval=60,
        timeout=86400,
        allowed_states=['success'],
        failed_states=['failed', 'upstream_failed'],
        retries=0,
        dag=dag,
        on_failure_callback=on_failure_callback
    )