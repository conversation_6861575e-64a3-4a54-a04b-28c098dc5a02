from datetime import timedelta
from airflow.sensors.external_task import ExternalTaskSensor

def sensor(ext_dag, dag, minutes_delta=0,on_failure_callback=None,external_task_id=None):
    """  The function sets up an ExternalTaskSensor to wait for a task in an external DAG to complete,
         with customizable task_id, external_dag_id, and execution_delta. If task_id is not provided, it defaults to wait_for_{dag}.
         The sensor keeps an eye on the external task every minute and will do so for 24 hrs before timing out.
    """

    def _infer_external_task_id(dag_id):
        """
        Infer the external task ID based on common patterns in the codebase.
        This function handles the specific naming conventions used in this project.
        """
        # Pattern 1: For DAGs ending with '_dag', try to find the main task
        if dag_id.endswith('_dag'):
            # Common patterns observed in the codebase:
            if 'intellisource_wc_patientaccounts_etl' in dag_id:
                return 'run_wc_patientaccounts_etl'
            elif 'analytics_groups_cron' in dag_id:
                return 'run_analytics_groups'
            elif 'intellisource_etl' in dag_id and 'wc_patientaccounts' not in dag_id:
                return 'run_intellisource_etl'
            else:
                # Fallback: replace '_dag' with '_task' (original logic)
                return dag_id.replace('_dag', '_task')

        # If no '_dag' suffix, return the dag_id as task_id
        return dag_id

    task_id = f"wait_for_{ext_dag.replace(' ', '_')}"

    # Use provided external_task_id or infer it intelligently
    inferred_task_id = external_task_id or _infer_external_task_id(ext_dag)

    ext_task_sensor = ExternalTaskSensor(
                    task_id=task_id,
                    external_dag_id=ext_dag,
                    external_task_id=inferred_task_id,
                    mode='reschedule',
                    poke_interval=60,
                    execution_delta=timedelta(minutes=minutes_delta),
                    timeout=86400,
                    failed_states=["failed", "upstream_failed"], # Fail fast on failure
                    retries=0, #No retries on sensor tasks, fails immediately.
                    dag=dag,
                    on_failure_callback=on_failure_callback
                    )

    return ext_task_sensor