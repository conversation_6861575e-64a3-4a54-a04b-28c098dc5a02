from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago

def task_wc_patientaccounts_etl():
    print("Running ETL for WC Patient Accounts...")

with DAG(
    dag_id="run_intellisource_wc_patientaccounts_etl_dag",
    default_args={"owner": "airflow", "start_date": days_ago(1)},
    schedule_interval=None,
    catchup=False,
) as dag:

    run_task = PythonOperator(
        task_id="run_wc_patientaccounts_etl",
        python_callable=task_wc_patientaccounts_etl,
    )
