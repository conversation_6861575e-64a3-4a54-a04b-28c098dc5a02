from airflow.providers.amazon.aws.hooks.sns import SnsHook
from airflow.utils.state import State


def send_combined_notification(context, status, server_name=None, env=None):
    """
    Sends notification to:
    - SNS email topic for all statuses.
    - SNS Slack topic only for 'fail' or 'retry' statuses.
    """

    sns_hook = SnsHook(aws_conn_id="AWS_SNS", region_name="us-east-1")
    ti = context['task_instance']
    dag_id = context['dag'].dag_id
    task_id = ti.task_id
    run_id = context.get('run_id')
    try_number = ti.try_number
    execution_date = str(context.get('execution_date'))
    log_url = ti.log_url
    operator = str(ti.operator)
    duration = ti.duration or "N/A"
    hostname = ti.hostname or "N/A"

    # Map state
    task_state = ti.state
    if task_state == State.UPSTREAM_FAILED:
        status_text = "UPSTREAM_FAILED"
    elif status == "success":
        status_text = "SUCCESS"
    elif status == "fail":
        status_text = "FAIL"
    elif status == "retry":
        status_text = "RETRY"
    else:
        status_text = "UNKNOWN"

    subject = f"{status_text}: {env} {dag_id}"

    message = (
        f"Status: {status_text}\n"
        f"DAG: {dag_id}\n"
        f"Task: {task_id}\n"
        f"Run ID: {run_id}\n"
        f"Execution Date: {execution_date}\n"
        f"Try Number: {try_number}\n"
        f"Operator: {operator}\n"
        f"Duration: {duration} sec\n"
        f"Hostname: {hostname}\n"
        f"Environment: {env}\n"
        f"Server: {server_name}\n"
        f"Logs: {log_url}\n"
    )

    # SNS topics (replace with your actual ARNs)
    sns_topic_arn = "arn:aws:sns:us-east-1:533267250305:sns"  # Email
    slack_topic_arn = "arn:aws:sns:us-east-1:533267250305:snstopic"  # Slack

    # Send SNS email notification (always)
    try:
        sns_hook.publish_to_target(
            target_arn=sns_topic_arn,
            message=message,
            subject=subject
        )
        print("[INFO] SNS email notification sent.")
    except Exception as e:
        print(f"[ERROR] Failed to send SNS email: {e}")

    # Send Slack notification (only on fail or retry)
    if status in ("fail", "retry"):
        try:
            sns_hook.publish_to_target(
                target_arn=slack_topic_arn,
                message=message,
                subject=subject
            )
            print("[INFO] SNS Slack notification sent.")
        except Exception as e:
            print(f"[ERROR] Failed to send SNS Slack message: {e}")

def get_on_failure_callback(server_name, env):
    return lambda context: send_combined_notification(
        context=context, status="fail", server_name=server_name, env=env
    )

def get_on_success_callback(server_name, env):
    return lambda context: send_combined_notification(
        context=context, status="success", server_name=server_name, env=env
    )

def get_on_retry_callback(server_name, env):
    return lambda context: send_combined_notification(
        context=context, status="retry", server_name=server_name, env=env
    )