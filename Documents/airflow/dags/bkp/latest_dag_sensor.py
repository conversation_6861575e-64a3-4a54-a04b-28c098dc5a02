from datetime import timed<PERSON><PERSON>
from airflow.sensors.base import BaseSensorOperator
from airflow.utils.decorators import apply_defaults
from airflow.models import DagRun, TaskInstance
from airflow.utils.db import provide_session
from airflow.utils.state import State


class LatestDagRunSensor(BaseSensorOperator):
    """
    Sensor that checks the latest DAG run status regardless of execution date.
    This sensor always looks for the most recent run of an external DAG.
    """
    
    @apply_defaults
    def __init__(
        self,
        external_dag_id,
        external_task_id=None,
        allowed_states=None,
        failed_states=None,
        *args,
        **kwargs
    ):
        super().__init__(*args, **kwargs)
        self.external_dag_id = external_dag_id
        self.external_task_id = external_task_id
        self.allowed_states = allowed_states or ['success']
        self.failed_states = failed_states or ['failed', 'upstream_failed']
    
    @provide_session
    def poke(self, context, session=None):
        """
        Check if the latest run of the external DAG/task is in the allowed state.
        """
        self.log.info(f"Checking latest run status for DAG: {self.external_dag_id}")
        
        try:
            # Get the most recent DAG run
            latest_dag_run = session.query(DagRun).filter(
                DagRun.dag_id == self.external_dag_id
            ).order_by(DagRun.execution_date.desc()).first()
            
            if not latest_dag_run:
                self.log.warning(f"No DAG runs found for {self.external_dag_id}")
                return False
            
            self.log.info(f"Latest DAG run: {latest_dag_run.run_id}, State: {latest_dag_run.state}, Execution Date: {latest_dag_run.execution_date}")
            
            # If no specific task is specified, check DAG run state
            if not self.external_task_id:
                dag_state = latest_dag_run.state
                if dag_state in self.allowed_states:
                    self.log.info(f"DAG {self.external_dag_id} is in allowed state: {dag_state}")
                    return True
                elif dag_state in self.failed_states:
                    self.log.error(f"DAG {self.external_dag_id} is in failed state: {dag_state}")
                    raise Exception(f"External DAG {self.external_dag_id} failed with state: {dag_state}")
                else:
                    self.log.info(f"DAG {self.external_dag_id} is in state: {dag_state}, waiting...")
                    return False
            
            # Check specific task state within the latest DAG run
            task_instance = session.query(TaskInstance).filter(
                TaskInstance.dag_id == self.external_dag_id,
                TaskInstance.task_id == self.external_task_id,
                TaskInstance.execution_date == latest_dag_run.execution_date
            ).first()
            
            if not task_instance:
                self.log.warning(f"Task {self.external_task_id} not found in latest run of {self.external_dag_id}")
                return False
            
            task_state = task_instance.state
            self.log.info(f"Task {self.external_task_id} state: {task_state}")
            
            if task_state in self.allowed_states:
                self.log.info(f"Task {self.external_task_id} is in allowed state: {task_state}")
                return True
            elif task_state in self.failed_states:
                self.log.error(f"Task {self.external_task_id} is in failed state: {task_state}")
                raise Exception(f"External task {self.external_dag_id}.{self.external_task_id} failed with state: {task_state}")
            else:
                self.log.info(f"Task {self.external_task_id} is in state: {task_state}, waiting...")
                return False
                
        except Exception as e:
            self.log.error(f"Error checking external DAG/task status: {e}")
            raise


def latest_dag_sensor(ext_dag, dag, external_task_id=None, minutes_delta=0, on_failure_callback=None):
    """
    Create a sensor that checks the latest DAG run status.
    
    Args:
        ext_dag: External DAG ID to monitor
        dag: Current DAG object
        external_task_id: Specific task ID to monitor (optional)
        minutes_delta: Not used in this sensor (kept for compatibility)
        on_failure_callback: Callback function for failures
    
    Returns:
        LatestDagRunSensor instance
    """
    
    # Auto-discover task ID if not provided
    if not external_task_id:
        # Use the same inference logic from task_sensor.py
        def _infer_external_task_id(dag_id):
            from airflow.models import TaskInstance, DagBag
            from airflow.utils.db import provide_session
            
            # Try database query first
            @provide_session
            def get_task_from_db(session=None):
                try:
                    task_instances = session.query(TaskInstance.task_id).filter(
                        TaskInstance.dag_id == dag_id
                    ).distinct().all()
                    
                    if task_instances:
                        task_ids = [ti.task_id for ti in task_instances]
                        for task_id in task_ids:
                            if task_id.startswith('run_') or 'task' in task_id:
                                return task_id
                        return task_ids[0]
                except:
                    pass
                return None
            
            # Try DAG introspection
            def get_task_from_dag():
                try:
                    dag_bag = DagBag()
                    if dag_id in dag_bag.dags:
                        external_dag = dag_bag.dags[dag_id]
                        task_ids = list(external_dag.task_ids)
                        if task_ids:
                            for task_id in task_ids:
                                if task_id.startswith('run_') or 'task' in task_id:
                                    return task_id
                            return task_ids[0]
                except:
                    pass
                return None
            
            # Try strategies
            task_id = get_task_from_db()
            if task_id:
                return task_id
                
            task_id = get_task_from_dag()
            if task_id:
                return task_id
                
            # Fallback
            if dag_id.endswith('_dag'):
                return dag_id.replace('_dag', '_task')
            return dag_id
        
        external_task_id = _infer_external_task_id(ext_dag)
    
    task_id = f"wait_for_{ext_dag.replace(' ', '_')}"
    
    return LatestDagRunSensor(
        task_id=task_id,
        external_dag_id=ext_dag,
        external_task_id=external_task_id,
        mode='reschedule',
        poke_interval=60,
        timeout=86400,
        allowed_states=['success'],
        failed_states=['failed', 'upstream_failed'],
        retries=0,
        dag=dag,
        on_failure_callback=on_failure_callback
    )
