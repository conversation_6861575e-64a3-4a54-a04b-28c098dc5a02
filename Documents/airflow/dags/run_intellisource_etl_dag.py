from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago

def task_intellisource_etl():
    print("Running Intellisource ETL...")

with DAG(
    dag_id="run_intellisource_etl_dag",
    default_args={"owner": "airflow", "start_date": days_ago(1)},
    schedule_interval=None,
    catchup=False,
) as dag:

    run_task = PythonOperator(
        task_id="run_intellisource_etl",
        python_callable=task_intellisource_etl,
    )
