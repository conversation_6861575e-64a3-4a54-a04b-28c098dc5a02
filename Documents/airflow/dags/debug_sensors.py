import sys
import os
sys.path.append(os.path.dirname(__file__))
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from datetime import timed<PERSON><PERSON>

def debug_external_tasks(**context):
    """Debug function to check the status of external DAGs and their tasks"""
    from airflow.models import DagRun, TaskInstance
    from airflow.utils.state import State
    from airflow.utils.db import provide_session
    
    @provide_session
    def get_external_task_status(session=None):
        external_dags = [
            "run_intellisource_wc_patientaccounts_etl_dag",
            "analytics_groups_cron_dag", 
            "run_intellisource_etl_dag"
        ]
        
        external_tasks = [
            "run_wc_patientaccounts_etl",
            "run_analytics_groups",
            "run_intellisource_etl"
        ]
        
        print("=== DEBUGGING EXTERNAL TASK SENSORS ===")
        
        for i, (dag_id, task_id) in enumerate(zip(external_dags, external_tasks)):
            print(f"\n--- Checking {dag_id} -> {task_id} ---")
            
            # Get recent DAG runs
            dag_runs = session.query(DagRun).filter(
                DagRun.dag_id == dag_id
            ).order_by(DagRun.execution_date.desc()).limit(5).all()
            
            if not dag_runs:
                print(f"❌ No DAG runs found for {dag_id}")
                continue
                
            print(f"📊 Found {len(dag_runs)} recent DAG runs:")
            
            for dag_run in dag_runs:
                print(f"  🔹 Run ID: {dag_run.run_id}")
                print(f"     Execution Date: {dag_run.execution_date}")
                print(f"     State: {dag_run.state}")
                print(f"     Start Date: {dag_run.start_date}")
                print(f"     End Date: {dag_run.end_date}")
                
                # Get task instances for this DAG run
                task_instances = session.query(TaskInstance).filter(
                    TaskInstance.dag_id == dag_id,
                    TaskInstance.execution_date == dag_run.execution_date,
                    TaskInstance.task_id == task_id
                ).all()
                
                if task_instances:
                    for ti in task_instances:
                        print(f"     ✅ Task {task_id}: {ti.state}")
                        print(f"        Start: {ti.start_date}")
                        print(f"        End: {ti.end_date}")
                else:
                    print(f"     ❌ No task instances found for {task_id}")
                print()
    
    get_external_task_status()

# Create debug DAG
with DAG(
    dag_id="debug_external_sensors",
    default_args={"owner": "airflow", "start_date": days_ago(1)},
    schedule_interval=None,
    catchup=False,
    description="Debug DAG to check external task statuses"
) as dag:

    debug_task = PythonOperator(
        task_id="debug_external_tasks",
        python_callable=debug_external_tasks,
        provide_context=True
    )
